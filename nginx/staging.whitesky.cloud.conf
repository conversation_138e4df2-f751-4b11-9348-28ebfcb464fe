# Redirect www to non-www
server {
    listen 80;
    server_name www.whitesky.lowie.overmeir.be;
    return 301 http://whitesky.lowie.overmeir.be$request_uri;
}

# Main server behind external SSL reverse proxy
server {
    listen 80;
    server_name whitesky.lowie.overmeir.be;

    root /var/www/staging;
    index index.html;

    # Trust reverse proxy headers
    real_ip_header X-Forwarded-For;
    set_real_ip_from 127.0.0.1;
    set_real_ip_from 10.0.0.0/8;
    set_real_ip_from ***********/16;

    # Enforce HTTPS (only if accessed directly and proto header exists)
    if ($http_x_forwarded_proto = "http") {
        return 301 https://$host$request_uri;
    }

    # Security Headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    add_header Cross-Origin-Opener-Policy "same-origin" always;
    add_header Cross-Origin-Embedder-Policy "require-corp" always;
    add_header Cross-Origin-Resource-Policy "same-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' https://cdnjs.cloudflare.com https://unpkg.com 'unsafe-inline'; style-src 'self' https://fonts.googleapis.com https://cdnjs.cloudflare.com 'unsafe-inline'; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:; frame-src 'self' https://www.youtube.com" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml image/svg+xml;

    location / {
        try_files $uri $uri/ =404;
    }

    location /submit-form {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Block access to hidden or sensitive files
    location ~* /\.(ht|git|env|DS_Store) {
        deny all;
    }
}