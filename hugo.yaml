baseURL: https://whitesky.cloud/
languageCode: en-us
title: whitesky cloud platform
theme: hugo-fresh

googleAnalytics: # Put in your tracking code without quotes like this: UA-XXX for universal tracking or G-XXX analytics v4 tracking
# Disables warnings
disableKinds:
- taxonomy
markup:
  goldmark:
    renderer:
      unsafe: true # Allows you to write raw html in your md files

params:
  # Open graph allows easy social sharing. If you don't want it you can set it to false or just delete the variable
  openGraph: true
  # Used as meta data; describe your site to make Google Bots happy
  description:
  # Preloader ensures images are loaded before displaying to the user. If you don't want it uncomment to set it to false
  # preloader: false
  navbarlogo:
    image: logo-blue-blacktext-small.png
    link: /
    width: 160
    height: 28
  font:
    name: "Open Sans"
    sizes: [400,600]
  hero:
    # Main hero title
    title: One cloud stack. Many locations. Full control.
    # Hero subtitle (optional)
    subtitle: Build distributed infrastructure that scales — across datacenters, partners, and borders.
    # Button text
    buttontext: Try now
    # Where the main hero button links to
    buttonlink: "/try-now"
    # Hero image (from static/images/___)
    image: cloud-worker-small.jpg
    # Footer logos (from static/images/logos/clients/*.svg)
    # urls are optional
    clientlogos:
    - logo: america-movil-gray
      url: https://www.americamovil.com/
    - logo: circlesco-logo-2
      url: https://circles.co
    - logo: exalate
      url: https://exalate.com
    - logo: leal
      url: https://www.lealgroup.mu
    - logo: varity
      url: https://www.varity.nl
  # Customizable navbar. For a dropdown, add a "sublinks" list.
  navbar:
  - title: Home
    url: "/"
  - title: How
    url: "/how"
  - title: Features
    url: /features
  - title: Federation
    url: /federation
  - title: Pricing
    url: /pricing
  - title: Blog
    url: /blog
  - title: Resources
    sublinks:
    - title: Documentation
      url: https://try.whitesky.cloud/docs/en/
    - title: APIs
      url: https://try.whitesky.cloud/api/1/
    - title: Contact & support
      url: /contact
    # - title: Fan shop
    #   url: /fan-shop
  - title: Try now
    url: /try-now
    button: true
  # sidebar:
  #   # Logo (from static/images/logos/___.svg)
  #   logo: fresh-square
  #   sections:
  #   - title: User
  #     icon: user
  #     links:
  #     - text: Profile
  #       url: /
  #     - text: Account
  #       url: /
  #     - text: Settings
  #       url: /
  #   - title: Messages
  #     icon: envelope
  #     links:
  #     - text: Inbox
  #       url: /
  #     - text: Compose
  #       url: /
  #   - title: Images
  #     icon: image
  #     links:
  #     - text: Library
  #       url: /
  #     - text: Upload
  #       url: /
  #   - title: Settings
  #     icon: cog
  #     links:
  #     - text: User settings
  #       url: /
  #     - text: App settings
  #       url: /
  section1:
    title: The alternative providing sovereignty with comfort
    subtitle: It feels like cloud, it is cloud, but it runs on your hardware in your (colo) datacenter.
    tiles:
    - title: European
      icon: fa-earth-europe
      text: The European answer to VMware, Nutanix, OpenStack and hyperscalers. Built for flexibility, compliance, sovereignty, performance and cooperation.
      #url: /
      #buttonText: Free trial
    - title: Federated
      icon: fa-project-diagram
      text: Connect and scale across multiple cloud locations — while keeping each environment independently controlled, secure, and locally compliant.
      url: /
      buttonText: Get started
    - title: High value at the right price
      icon: fa-balance-scale
      text: Get premium cloud capabilities without hyperscaler complexity or cost. whitesky offers predictable pricing and tailored solutions that scale with your business — not your overhead.
      url: /
      buttonText: Get started
  section2:
    title: A Vanilla Cloud Stack
    subtitle: We believe in choice — not lock-in. Our platform earns loyalty through performance, not constraints.
    features:
    - title: Cloudspaces
      text: "**Fully isolated virtual environments** where you deploy and manage **virtual machines**, VGPUs, **networking**, load balancers, reverse proxies, backups, DNS, SSL certificates, vTPM, secure boot and storage. Cloudspaces give you granular control, anti-affinity policies, and the ability to attach both **out-of-the-box software-defined and direct NVMe storage** to your workloads."
      # Icon (Font Awesome icon class)
      icon: fa-cloud
    - title: Objectspaces
      text: "**Scalable, S3-compatible object storage** with versioning and **object locking** built in. Objectspaces allow you to store, protect, and serve unstructured data reliably — whether you're backing up virtual machines, hosting static websites, or integrating with cloud-native apps. A single deployment can scale up to 360PB."
      icon: fa-box
    - title: Containerspaces
      text: "**Fully managed Kubernetes clusters** with built-in multi-site and geo-redundancy capabilities. Containerspaces let you run containerized workloads with seamless integration into the portal, billing, and networking with your virtual machines and storage."
      icon: fa-cubes
    - title: Billing system
      text: "**Built-in billing and invoicing system** designed for MSPs, SaaS providers, and internal IT teams. Whether you're reselling cloud capacity or allocating internal costs, the billing engine supports **usage-based metering, customer invoicing, reseller onboarding, and federation** with other whitesky.cloud providers — all integrated into the platform."
      icon: fa-receipt
  section3:
    title: Your cloud.
    subtitle: Deploy and scale on infrastructure you own — or across trusted whitesky locations.
    image: portal.jpg
    buttonText: Try now
    buttonLink: "/try-now"
  section4:
    title: Trusted by teams building critical infrastructure
    subtitle: From telecom to SaaS — see how our clients rely on whitesky to run and grow with confidence.
    clients:
    - name: Nestor Lopez
      quote: whitesky.cloud gives us the flexibility and control to run secure, stable infrastructure across the América Móvil network. The platform is open, customizable, and easy to manage.
      job: Senior project manager core networks
      img: 3
    - name: Francis Martens
      quote: With whitesky.cloud, we get a reliable cloud foundation tailored to our needs — fully private, performant, and backed by a team that understands our growth as a SaaS company.
      job: Chief Executive Officer
      img: 2
    - name: M. Pothunnah
      quote: whitesky.cloud has supported our private cloud journey for years, helping us as an MSP to expand our services and streamline our operations. Their team brings insight and ongoing improvements.
      job: Operations manager critical services
      img: 3
  section5:
    title: Drop us a line or two
    subtitle: We'd love to hear from you
    buttonText: Send Message
    TODO: update this
    action: /submit-form
    method: POST
  footer:
    # Logo (from static/images/logos/___)
    logo: ws_logo_white_vertical_v1.webp
    # Social Media Title
    socialmediatitle: Follow Us on Linkedin
    # Social media links (GitHub, Twitter, etc.). All are optional.
    socialmedia:
    - link: https://www.linkedin.com/company/whitesky-cloud
      icon: linkedin
    bulmalogo: true
    quicklinks:
      column1:
        title: "Product"
        links:
        - text: Discover features
          link: /features
        - text: Use Cases
          link: /use-cases
        - text: Our roadmap
          link: /roadmap
      column2:
        title: "Docs"
        links:
        - text: Get started
          link: https://try.whitesky.cloud/docs/en/concepts/
        - text: Admin guide
          link: https://try.whitesky.cloud/docs/en/admin/
        - text: APIs
          link: https://try.whitesky.cloud/api/1/
      column3:
        title: "Blog"
        links:
        - text: Latest news
          link: /blog
